# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 核心任务

完善 `C:\Users\<USER>\Desktop\EnigmaX1` 目录下的FPGA项目文件，使其在硬件行为上精确匹配位于 `C:\Users\<USER>\Desktop\EnigmaX1\data` 目录下的真实硬件数据。

## 绝对约束 (Always Enforced)

### 数据源约束
- **唯一模板**: `C:\Users\<USER>\Desktop\EnigmaX1\EnigmaX1` 目录是唯一的结构和格式参考模板
- **唯一数据源**: `C:\Users\<USER>\Desktop\EnigmaX1\data` 目录是所有数值的唯一来源
- **禁止捏造**: 不允许使用任何非数据源中提供的数值
- **真实数据基础**: 全部数据来源基于真实采集的数据，不可以凭空捏造

### 文件操作约束
- **禁止修改模板**: 严禁修改EnigmaX1目录中的任何文件，该目录仅作参考
- **修改目标**: 只能修改rtl8111根目录下的ip目录和src目录中的所有文件
- **禁止文件操作**: 严禁创建、删除、重命名或移动任何文件
- **内部修改**: 所有修改必须在现有文件内部完成
- **禁止注释**: 不要在任何代码文件中添加或修改注释
- **完整读取**: 读取所有文件都需要完全阅读，不许只读前几行

### 动态适配约束
- **目标**: 制作基于PCILeech框架的以假乱真RTL8111网卡
- **全机适配**: 必须适配所有电脑系统
- **动态数据**: 将采集的特定电脑数据转换为动态数据，确保在任意Windows系统中都是完全仿真的网卡
- **完全替换**: 所有需要修改的值都必须修改成采集的真实参数和数据

### 工作方式约束
- **目标**: 工作是"完善"，即在现有文件框架内，用真实数据替换占位符或不正确的值
- **不许自动compact**: compact前需要询问是否需要

## 项目架构

### 关键目录结构
- **src/**: SystemVerilog源文件
- **ip/**: Xilinx IP核配置文件
- **data/**: 真实硬件数据文件
  - `rtl8111_config.hex`: 配置空间镜像
  - `idle_8111_trace.log`: 硬件行为跟踪
  - `real_register_analysis.md`: 寄存器分析
  - `终极指南.md`: 实现指南
- **EnigmaX1/**: 参考模板目录

### 核心构建命令

```bash
# 生成项目文件
source vivado_generate_project.tcl -notrace

# 构建比特流 (~1小时)
source vivado_build.tcl -notrace

# 烧录到硬件
source vivado_flash.tcl -notrace
```

### 工具要求
- Xilinx Vivado WebPACK 2023.2或更高版本
- 构建路径要求短路径，避免路径过长导致失败

## RTL8111设备参数 (来自data目录)

### 设备标识
- Vendor ID: 0x10EC (Realtek)
- Device ID: 0x8168 (RTL8111/8168/8411)
- Subsystem Vendor ID: 0x1462 (MSI)
- Subsystem ID: 0x7C82
- Class Code: 0x020000 (以太网控制器)

### 关键寄存器 (基于real_register_analysis.md)
- **0x40 (CHIP_ID)**: 0x57100F80
- **0x37 (CMD)**: 初始值0x0C，重置后0x00
- **0x44 (TCR)**: 0x2CF0E
- **0xB0 (PHYACCESS)**: 间接PHY访问寄存器
- **0xB8 (PHYSTAT)**: PHY状态寄存器

## 相关项目路径

学习其它的pcileech项目路径：
- D:\pci1\pcileech-audio-main
- D:\pci1\pcileech-multimedia-hd-main
- D:\pci1\pcileech-wifi-DWA-556

## 重要提醒

- FPGA编程必须在主机启动前完成
- 烧录时使用内存部件: `is25lp128f`
- 构建时间约1小时，需要耐心等待
- 仅用于防御性安全研究和分析