{"schema": "xilinx.com:schema:json_instance:1.0", "ip_inst": {"xci_name": "pcie_7x_0", "component_reference": "xilinx.com:ip:pcie_7x:3.3", "ip_revision": "20", "gen_directory": "../../../../pcileech_enigma_x1.gen/sources_1/ip/pcie_7x_0", "parameters": {"component_parameters": {"mode_selection": [{"value": "Advanced", "value_src": "user", "resolve_type": "user", "usage": "all"}], "pcie_id_if": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Use_Class_Code_Lookup_Assistant": [{"value": "false", "resolve_type": "user", "usage": "all"}], "Component_Name": [{"value": "pcie_7x_0", "resolve_type": "user", "usage": "all"}], "Device_Port_Type": [{"value": "PCI_Express_Endpoint_device", "resolve_type": "user", "usage": "all"}], "Maximum_Link_Width": [{"value": "X1", "resolve_type": "user", "usage": "all"}], "Link_Speed": [{"value": "2.5_GT/s", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Interface_Width": [{"value": "64_bit", "value_src": "user", "resolve_type": "user", "usage": "all"}], "User_Clk_Freq": [{"value": "125", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Bar0_Enabled": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar0_Type": [{"value": "IO", "resolve_type": "user", "usage": "all"}], "Bar0_64bit": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar0_Prefetchable": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar0_Scale": [{"value": "Bytes", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Bar0_Size": [{"value": "256", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Bar1_Enabled": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar1_Type": [{"value": "N/A", "resolve_type": "user", "usage": "all"}], "Bar1_64bit": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar1_Prefetchable": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar1_Scale": [{"value": "Kilobytes", "resolve_type": "user", "usage": "all"}], "Bar1_Size": [{"value": "2", "resolve_type": "user", "usage": "all"}], "Bar2_Enabled": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar2_Type": [{"value": "Memory", "resolve_type": "user", "usage": "all"}], "Bar2_64bit": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar2_Prefetchable": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar2_Scale": [{"value": "Kilobytes", "resolve_type": "user", "usage": "all"}], "Bar2_Size": [{"value": "4", "resolve_type": "user", "usage": "all"}], "Bar3_Enabled": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar3_Type": [{"value": "N/A", "resolve_type": "user", "usage": "all"}], "Bar3_64bit": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar3_Prefetchable": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar3_Scale": [{"value": "Kilobytes", "resolve_type": "user", "usage": "all"}], "Bar3_Size": [{"value": "2", "resolve_type": "user", "usage": "all"}], "Bar4_Enabled": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar4_Type": [{"value": "Memory", "resolve_type": "user", "usage": "all"}], "Bar4_64bit": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar4_Prefetchable": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar4_Scale": [{"value": "Kilobytes", "resolve_type": "user", "usage": "all"}], "Bar4_Size": [{"value": "16", "resolve_type": "user", "usage": "all"}], "Bar5_Enabled": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar5_Type": [{"value": "N/A", "resolve_type": "user", "usage": "all"}], "Bar5_Prefetchable": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Bar5_Scale": [{"value": "Kilobytes", "resolve_type": "user", "usage": "all"}], "Bar5_Size": [{"value": "2", "resolve_type": "user", "usage": "all"}], "Expansion_Rom_Enabled": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Expansion_Rom_Scale": [{"value": "Kilobytes", "resolve_type": "user", "usage": "all"}], "Expansion_Rom_Size": [{"value": "2", "resolve_type": "user", "usage": "all"}], "IO_Base_Limit_Registers": [{"value": "Disabled", "resolve_type": "user", "usage": "all"}], "Prefetchable_Memory_Base_Limit_Registers": [{"value": "Disabled", "resolve_type": "user", "usage": "all"}], "Vendor_ID": [{"value": "10EC", "resolve_type": "user", "usage": "all"}], "Device_ID": [{"value": "8168", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Revision_ID": [{"value": "15", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Subsystem_Vendor_ID": [{"value": "10EC", "resolve_type": "user", "usage": "all"}], "Subsystem_ID": [{"value": "8168", "resolve_type": "user", "usage": "all"}], "Class_Code_Base": [{"value": "02", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Class_Code_Sub": [{"value": "00", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Class_Code_Interface": [{"value": "00", "resolve_type": "user", "usage": "all"}], "Base_Class_Menu": [{"value": "Network_controller", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Sub_Class_Interface_Menu": [{"value": "Ethernet_controller", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Cardbus_CIS_Pointer": [{"value": "00000000", "resolve_type": "user", "usage": "all"}], "PCIe_Cap_Slot_Implemented": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Max_Payload_Size": [{"value": "128_bytes", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Extended_Tag_Field": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "Extended_Tag_Default": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "Phantom_Functions": [{"value": "No_function_number_bits_used", "resolve_type": "user", "usage": "all"}], "Acceptable_L0s_Latency": [{"value": "64ns_to_128ns", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Acceptable_L1_Latency": [{"value": "1us_to_2us", "resolve_type": "user", "usage": "all"}], "Cpl_Finite": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Cpl_Timeout_Disable_Sup": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Cpl_Timeout_Range": [{"value": "Range_B", "resolve_type": "user", "usage": "all"}], "Buf_Opt_BMA": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "Perf_Level": [{"value": "High", "resolve_type": "user", "usage": "all"}], "Dll_Link_Active_Cap": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "RCB": [{"value": "64_byte", "resolve_type": "user", "usage": "all"}], "Trgt_Link_Speed": [{"value": "4'h1", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Hw_Auton_Spd_Disable": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "De_emph": [{"value": "-3.5", "resolve_type": "user", "usage": "all"}], "Enable_Slot_Clock_Cfg": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Root_Cap_CRS": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Slot_Cap_Attn_Butn": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Slot_Cap_Pwr_Ctrl": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Slot_Cap_MRL": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Slot_Cap_Attn_Ind": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Slot_Cap_Pwr_Ind": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Slot_Cap_HotPlug_Surprise": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Slot_Cap_HotPlug_Cap": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Slot_Cap_Elec_Interlock": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Slot_Cap_No_Cmd_Comp_Sup": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Slot_Cap_Pwr_Limit_Value": [{"value": "0", "resolve_type": "user", "usage": "all"}], "Slot_Cap_Pwr_Limit_Scale": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "Slot_Cap_Physical_Slot_Num": [{"value": "0", "resolve_type": "user", "usage": "all"}], "IntX_Generation": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Legacy_Interrupt": [{"value": "INTA", "resolve_type": "user", "usage": "all"}], "MSI_Enabled": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "MSI_64b": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Multiple_Message_Capable": [{"value": "4_vectors", "resolve_type": "user", "usage": "all"}], "MSI_Vec_Mask": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "MSIx_Enabled": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "MSIx_Table_Size": [{"value": "4", "resolve_type": "user", "usage": "all"}], "MSIx_Table_Offset": [{"value": "0", "resolve_type": "user", "usage": "all"}], "MSIx_Table_BIR": [{"value": "BAR_0", "resolve_type": "user", "usage": "all"}], "MSIx_PBA_Offset": [{"value": "0", "resolve_type": "user", "usage": "all"}], "MSIx_PBA_BIR": [{"value": "BAR_0", "resolve_type": "user", "usage": "all"}], "Device_Specific_Initialization": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "D1_Support": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "D2_Support": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "D0_PME_Support": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "D1_PME_Support": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "D2_PME_Support": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "D3hot_PME_Support": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "D3cold_PME_Support": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "No_Soft_Reset": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "D0_Power_Consumed": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D0_Power_Consumed_Factor": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D1_Power_Consumed": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D1_Power_Consumed_Factor": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D2_Power_Consumed": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D2_Power_Consumed_Factor": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D3_Power_Consumed": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D3_Power_Consumed_Factor": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D0_Power_Dissipated": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D0_Power_Dissipated_Factor": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D1_Power_Dissipated": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D1_Power_Dissipated_Factor": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D2_Power_Dissipated": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D2_Power_Dissipated_Factor": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D3_Power_Dissipated": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "D3_Power_Dissipated_Factor": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "DSN_Enabled": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "VC_Cap_Enabled": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "VC_Cap_Reject_Snoop": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "VSEC_Enabled": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "PCI_CFG_Space": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "PCI_CFG_Space_Addr": [{"value": "2A", "value_src": "user", "resolve_type": "user", "usage": "all"}], "EXT_PCI_CFG_Space": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "EXT_PCI_CFG_Space_Addr": [{"value": "043", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Xlnx_Ref_Board": [{"value": "None", "resolve_type": "user", "usage": "all"}], "PCIe_Blk_Locn": [{"value": "X0Y0", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Trans_Buf_Pipeline": [{"value": "None", "value_src": "user", "resolve_type": "user", "usage": "all"}], "En_route_unlock": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "En_route_pme_to": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "En_route_err_cor": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "En_route_err_nfl": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "En_route_err_ftl": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "En_route_inta": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "En_route_intb": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "En_route_intc": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "En_route_intd": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "En_route_pm_pme": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "En_route_pme_to_ack": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Receive_NP_Request": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Enable_ACK_NAK_Timer": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "ACK_NAK_Timeout_Func": [{"value": "Absolute", "resolve_type": "user", "usage": "all"}], "ACK_NAK_Timeout_Value": [{"value": "0000", "resolve_type": "user", "usage": "all"}], "Enable_Replay_Timer": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Replay_Timeout_Func": [{"value": "Add", "resolve_type": "user", "usage": "all"}], "Replay_Timeout_Value": [{"value": "0000", "resolve_type": "user", "usage": "all"}], "Enable_Lane_Reversal": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Upconfigure_Capable": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Force_No_Scrambling": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Disable_Tx_ASPM_L0s": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Downstream_Link_Num": [{"value": "00", "resolve_type": "user", "usage": "all"}], "UR_INV_REQ": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "UR_PRS_RESPONSE": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Silicon_Rev": [{"value": "GES_and_Production", "resolve_type": "user", "usage": "all"}], "Pcie_fast_config": [{"value": "None", "resolve_type": "user", "usage": "all"}], "PCIe_Debug_Ports": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "Ref_Clk_Freq": [{"value": "100_MHz", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Cost_Table": [{"value": "1", "resolve_type": "user", "format": "long", "usage": "all"}], "UR_Atomic": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "ATOMICOP32_Completer_Supported": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "ATOMICOP64_Completer_Supported": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "CAS128_Completer_Supported": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "TPH_Completer_Supported": [{"value": "00", "resolve_type": "user", "usage": "all"}], "ARI_Forwarding_Supported": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AtomicOp_Routing_Supported": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "ASPM_Optionality": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Enabled": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_ECRC_Check_Capable": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_ECRC_Gen_Capable": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Multiheader": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Permit_Root_Error_Update": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Correctable_Internal_Error": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Header_Log_Overflow": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Receiver_Error": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Surprise_Down": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Flow_Control_Protocol_Error": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Completion_Timeout": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Completer_Abort": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Receiver_Overflow": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_ECRC_Error": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_ACS_Violation": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_Uncorrectable_Internal_Error": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_MC_Blocked_TLP": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_AtomicOp_Egress_Blocked": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "AER_TLP_Prefix_Blocked": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Optional_Error_Support": [{"value": "000000", "resolve_type": "user", "usage": "all"}], "RBAR_Enabled": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "RBAR_Num": [{"value": "0", "resolve_type": "user", "usage": "all"}], "BAR_Index_Value0": [{"value": "0", "resolve_type": "user", "usage": "all"}], "BAR0_Size_Vector": [{"value": "1M", "resolve_type": "user", "usage": "all"}], "RBAR_Initial_Value0": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "BAR_Index_Value1": [{"value": "0", "resolve_type": "user", "usage": "all"}], "BAR1_Size_Vector": [{"value": "1M", "resolve_type": "user", "usage": "all"}], "RBAR_Initial_Value1": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "BAR_Index_Value2": [{"value": "0", "resolve_type": "user", "usage": "all"}], "BAR2_Size_Vector": [{"value": "1M", "resolve_type": "user", "usage": "all"}], "RBAR_Initial_Value2": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "BAR_Index_Value3": [{"value": "0", "resolve_type": "user", "usage": "all"}], "BAR3_Size_Vector": [{"value": "1M", "resolve_type": "user", "usage": "all"}], "RBAR_Initial_Value3": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "BAR_Index_Value4": [{"value": "0", "resolve_type": "user", "usage": "all"}], "BAR4_Size_Vector": [{"value": "1M", "resolve_type": "user", "usage": "all"}], "RBAR_Initial_Value4": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "BAR_Index_Value5": [{"value": "0", "resolve_type": "user", "usage": "all"}], "BAR5_Size_Vector": [{"value": "1M", "resolve_type": "user", "usage": "all"}], "RBAR_Initial_Value5": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}], "RECRC_Check": [{"value": "0", "resolve_type": "user", "usage": "all"}], "RECRC_Check_Trim": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Disable_Rx_Poisoned_Resp": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "pipe_sim": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "en_ext_clk": [{"value": "false", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "en_ext_gt_common": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "en_ext_ch_gt_drp": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "en_transceiver_status_ports": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "shared_logic_in_core": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "pl_interface": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "cfg_mgmt_if": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "cfg_ctl_if": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "cfg_status_if": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "rcv_msg_if": [{"value": "false", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "cfg_fc_if": [{"value": "false", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "err_reporting_if": [{"value": "false", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "USE_BOARD_FLOW": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "RESET_BOARD_INTERFACE": [{"value": "Custom", "resolve_type": "user", "usage": "all"}], "en_ext_pipe_interface": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "en_ext_startup": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "pipe_mode_sim": [{"value": "None", "value_src": "user", "resolve_type": "user", "usage": "all"}], "enable_jtag_dbg": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "reduce_oob_freq": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "p2_en_cof_int_1": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}]}, "model_parameters": {"PCIE_ID_IF": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_component_name": [{"value": "pcie_7x_0", "resolve_type": "generated", "usage": "all"}], "dev_port_type": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "c_dev_port_type": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_header_type": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "c_upstream_facing": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "max_lnk_wdt": [{"value": "000001", "resolve_type": "generated", "usage": "all"}], "max_lnk_spd": [{"value": "2", "resolve_type": "generated", "usage": "all"}], "c_gen1": [{"value": "true", "resolve_type": "generated", "format": "bool", "usage": "all"}], "pci_exp_int_freq": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "c_pcie_fast_config": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "bar_0": [{"value": "FFFFFF01", "resolve_type": "generated", "usage": "all"}], "bar_1": [{"value": "00000000", "resolve_type": "generated", "usage": "all"}], "bar_2": [{"value": "FFFFF00C", "resolve_type": "generated", "usage": "all"}], "bar_3": [{"value": "00000000", "resolve_type": "generated", "usage": "all"}], "bar_4": [{"value": "FFFFC00C", "resolve_type": "generated", "usage": "all"}], "bar_5": [{"value": "00000000", "resolve_type": "generated", "usage": "all"}], "xrom_bar": [{"value": "00000000", "resolve_type": "generated", "usage": "all"}], "cost_table": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "ven_id": [{"value": "10EC", "resolve_type": "generated", "usage": "all"}], "dev_id": [{"value": "8168", "resolve_type": "generated", "usage": "all"}], "rev_id": [{"value": "15", "resolve_type": "generated", "usage": "all"}], "subsys_ven_id": [{"value": "1462", "resolve_type": "generated", "usage": "all"}], "subsys_id": [{"value": "7990", "resolve_type": "generated", "usage": "all"}], "class_code": [{"value": "020000", "resolve_type": "generated", "usage": "all"}], "cardbus_cis_ptr": [{"value": "00000000", "resolve_type": "generated", "usage": "all"}], "cap_ver": [{"value": "2", "resolve_type": "generated", "usage": "all"}], "c_pcie_cap_slot_implemented": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "mps": [{"value": "010", "resolve_type": "generated", "usage": "all"}], "cmps": [{"value": "2", "resolve_type": "generated", "usage": "all"}], "ext_tag_fld_sup": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_dev_control_ext_tag_default": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "phantm_func_sup": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "c_phantom_functions": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "ep_l0s_accpt_lat": [{"value": "111", "resolve_type": "generated", "usage": "all"}], "c_ep_l0s_accpt_lat": [{"value": "7", "resolve_type": "generated", "usage": "all"}], "ep_l1_accpt_lat": [{"value": "111", "resolve_type": "generated", "usage": "all"}], "c_ep_l1_accpt_lat": [{"value": "7", "resolve_type": "generated", "usage": "all"}], "c_cpl_timeout_disable_sup": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_cpl_timeout_range": [{"value": "0010", "resolve_type": "generated", "usage": "all"}], "c_cpl_timeout_ranges_sup": [{"value": "2", "resolve_type": "generated", "usage": "all"}], "c_buf_opt_bma": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_perf_level_high": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_tx_last_tlp": [{"value": "29", "resolve_type": "generated", "usage": "all"}], "c_rx_ram_limit": [{"value": "7FF", "resolve_type": "generated", "usage": "all"}], "c_fc_ph": [{"value": "4", "resolve_type": "generated", "usage": "all"}], "c_fc_pd": [{"value": "64", "resolve_type": "generated", "usage": "all"}], "c_fc_nph": [{"value": "4", "resolve_type": "generated", "usage": "all"}], "c_fc_npd": [{"value": "8", "resolve_type": "generated", "usage": "all"}], "c_fc_cplh": [{"value": "72", "resolve_type": "generated", "usage": "all"}], "c_fc_cpld": [{"value": "850", "resolve_type": "generated", "usage": "all"}], "c_cpl_inf": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_cpl_infinite": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_dll_lnk_actv_cap": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_trgt_lnk_spd": [{"value": "2", "resolve_type": "generated", "usage": "all"}], "c_hw_auton_spd_disable": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_de_emph": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "slot_clk": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_rcb": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_root_cap_crs": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_attn_butn": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_attn_ind": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_pwr_ctrl": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_pwr_ind": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_hotplug_surprise": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_hotplug_cap": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_mrl": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_elec_interlock": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_no_cmd_comp_sup": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_pwr_limit_value": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_pwr_limit_scale": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_slot_cap_physical_slot_num": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "intx": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "int_pin": [{"value": "1", "resolve_type": "generated", "usage": "all"}], "c_msi_cap_on": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_pm_cap_next_ptr": [{"value": "48", "resolve_type": "generated", "usage": "all"}], "c_msi_64b_addr": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_msi": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_msi_mult_msg_extn": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_msi_per_vctr_mask_cap": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_msix_cap_on": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_msix_next_ptr": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "c_pcie_cap_next_ptr": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "c_msix_table_size": [{"value": "000", "resolve_type": "generated", "usage": "all"}], "c_msix_table_offset": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_msix_table_bir": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_msix_pba_offset": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_msix_pba_bir": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "dsi": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_dsi_bool": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "d1_sup": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_d1_support": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "d2_sup": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_d2_support": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "pme_sup": [{"value": "0F", "resolve_type": "generated", "usage": "all"}], "c_pme_support": [{"value": "0F", "resolve_type": "generated", "usage": "all"}], "no_soft_rst": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "pwr_con_d0_state": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "con_scl_fctr_d0_state": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "pwr_con_d1_state": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "con_scl_fctr_d1_state": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "pwr_con_d2_state": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "con_scl_fctr_d2_state": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "pwr_con_d3_state": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "con_scl_fctr_d3_state": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "pwr_dis_d0_state": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "dis_scl_fctr_d0_state": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "pwr_dis_d1_state": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "dis_scl_fctr_d1_state": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "pwr_dis_d2_state": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "dis_scl_fctr_d2_state": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "pwr_dis_d3_state": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "dis_scl_fctr_d3_state": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_dsn_cap_enabled": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_dsn_base_ptr": [{"value": "100", "resolve_type": "generated", "usage": "all"}], "c_vc_cap_enabled": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_vc_base_ptr": [{"value": "000", "resolve_type": "generated", "usage": "all"}], "c_vc_cap_reject_snoop": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_vsec_cap_enabled": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_vsec_base_ptr": [{"value": "000", "resolve_type": "generated", "usage": "all"}], "c_vsec_next_ptr": [{"value": "000", "resolve_type": "generated", "usage": "all"}], "c_dsn_next_ptr": [{"value": "10c", "resolve_type": "generated", "usage": "all"}], "c_vc_next_ptr": [{"value": "000", "resolve_type": "generated", "usage": "all"}], "c_pci_cfg_space_addr": [{"value": "2A", "resolve_type": "generated", "usage": "all"}], "c_ext_pci_cfg_space_addr": [{"value": "043", "resolve_type": "generated", "usage": "all"}], "c_last_cfg_dw": [{"value": "10C", "resolve_type": "generated", "usage": "all"}], "c_enable_msg_route": [{"value": "00000000000", "resolve_type": "generated", "usage": "all"}], "bram_lat": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_rx_raddr_lat": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_rx_rdata_lat": [{"value": "2", "resolve_type": "generated", "usage": "all"}], "c_rx_write_lat": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_tx_raddr_lat": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_tx_rdata_lat": [{"value": "2", "resolve_type": "generated", "usage": "all"}], "c_tx_write_lat": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_ll_ack_timeout_enable": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_ll_ack_timeout_function": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_ll_ack_timeout": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "c_ll_replay_timeout_enable": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_ll_replay_timeout_func": [{"value": "1", "resolve_type": "generated", "usage": "all"}], "c_ll_replay_timeout": [{"value": "0000", "resolve_type": "generated", "usage": "all"}], "c_dis_lane_reverse": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_upconfig_capable": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_disable_scrambling": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_disable_tx_aspm_l0s": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_pcie_dbg_ports": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "pci_exp_ref_freq": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_xlnx_ref_board": [{"value": "NONE", "resolve_type": "generated", "usage": "all"}], "c_pcie_blk_locn": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_ur_atomic": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_dev_cap2_atomicop32_completer_supported": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_dev_cap2_atomicop64_completer_supported": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_dev_cap2_cas128_completer_supported": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_dev_cap2_tph_completer_supported": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "c_dev_cap2_ari_forwarding_supported": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_dev_cap2_atomicop_routing_supported": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_link_cap_aspm_optionality": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_aer_cap_on": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_aer_base_ptr": [{"value": "000", "resolve_type": "generated", "usage": "all"}], "c_aer_cap_nextptr": [{"value": "000", "resolve_type": "generated", "usage": "all"}], "c_aer_cap_ecrc_check_capable": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_aer_cap_ecrc_gen_capable": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_aer_cap_multiheader": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_aer_cap_permit_rooterr_update": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_on": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_rbar_base_ptr": [{"value": "000", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_nextptr": [{"value": "000", "resolve_type": "generated", "usage": "all"}], "c_rbar_num": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_sup0": [{"value": "00001", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_index0": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_control_encodedbar0": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_sup1": [{"value": "00001", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_index1": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_control_encodedbar1": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_sup2": [{"value": "00001", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_index2": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_control_encodedbar2": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_sup3": [{"value": "00001", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_index3": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_control_encodedbar3": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_sup4": [{"value": "00001", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_index4": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_control_encodedbar4": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_sup5": [{"value": "00001", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_index5": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_rbar_cap_control_encodedbar5": [{"value": "00", "resolve_type": "generated", "usage": "all"}], "c_recrc_check": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "c_recrc_check_trim": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_disable_rx_poisoned_resp": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "c_trn_np_fc": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_ur_inv_req": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_ur_prs_response": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "c_silicon_rev": [{"value": "2", "resolve_type": "generated", "usage": "all"}], "c_aer_cap_optional_err_support": [{"value": "000000", "resolve_type": "generated", "usage": "all"}], "LINK_CAP_MAX_LINK_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DATA_WIDTH": [{"value": "64", "resolve_type": "generated", "format": "long", "usage": "all"}], "PIPE_SIM": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "PCIE_EXT_CLK": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "PCIE_EXT_GT_COMMON": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "EXT_CH_GT_DRP": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "TRANSCEIVER_CTRL_STATUS_PORTS": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "SHARED_LOGIC_IN_CORE": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "ERR_REPORTING_IF": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "PL_INTERFACE": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "CFG_MGMT_IF": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "CFG_CTL_IF": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "CFG_STATUS_IF": [{"value": "TRUE", "resolve_type": "generated", "usage": "all"}], "RCV_MSG_IF": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "CFG_FC_IF": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "EXT_PIPE_INTERFACE": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "EXT_STARTUP_PRIMITIVE": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "KEEP_WIDTH": [{"value": "8", "resolve_type": "dependent", "format": "long", "usage": "all"}], "PCIE_ASYNC_EN": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "ENABLE_JTAG_DBG": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}], "REDUCE_OOB_FREQ": [{"value": "FALSE", "resolve_type": "generated", "usage": "all"}]}, "project_parameters": {"ARCHITECTURE": [{"value": "artix7"}], "BASE_BOARD_PART": [{"value": ""}], "BOARD_CONNECTIONS": [{"value": ""}], "DEVICE": [{"value": "xc7a75t"}], "PACKAGE": [{"value": "fgg484"}], "PREFHDL": [{"value": "VERILOG"}], "SILICON_REVISION": [{"value": ""}], "SIMULATOR_LANGUAGE": [{"value": "MIXED"}], "SPEEDGRADE": [{"value": "-2"}], "STATIC_POWER": [{"value": ""}], "TEMPERATURE_GRADE": [{"value": ""}], "USE_RDI_CUSTOMIZATION": [{"value": "TRUE"}], "USE_RDI_GENERATION": [{"value": "TRUE"}]}, "runtime_parameters": {"IPCONTEXT": [{"value": "IP_Flow"}], "IPREVISION": [{"value": "20"}], "MANAGED": [{"value": "TRUE"}], "OUTPUTDIR": [{"value": "../../../../pcileech_enigma_x1.gen/sources_1/ip/pcie_7x_0"}], "SELECTEDSIMMODEL": [{"value": ""}], "SHAREDDIR": [{"value": "."}], "SWVERSION": [{"value": "2023.2"}], "SYNTHESISFLOW": [{"value": "OUT_OF_CONTEXT"}]}}, "boundary": {"ports": {"pci_exp_txp": [{"direction": "out", "size_left": "0", "size_right": "0"}], "pci_exp_txn": [{"direction": "out", "size_left": "0", "size_right": "0"}], "pci_exp_rxp": [{"direction": "in", "size_left": "0", "size_right": "0"}], "pci_exp_rxn": [{"direction": "in", "size_left": "0", "size_right": "0"}], "user_clk_out": [{"direction": "out"}], "user_reset_out": [{"direction": "out"}], "user_lnk_up": [{"direction": "out"}], "user_app_rdy": [{"direction": "out"}], "tx_buf_av": [{"direction": "out", "size_left": "5", "size_right": "0"}], "tx_cfg_req": [{"direction": "out"}], "tx_err_drop": [{"direction": "out"}], "s_axis_tx_tready": [{"direction": "out"}], "s_axis_tx_tdata": [{"direction": "in", "size_left": "63", "size_right": "0"}], "s_axis_tx_tkeep": [{"direction": "in", "size_left": "7", "size_right": "0"}], "s_axis_tx_tlast": [{"direction": "in"}], "s_axis_tx_tvalid": [{"direction": "in"}], "s_axis_tx_tuser": [{"direction": "in", "size_left": "3", "size_right": "0"}], "tx_cfg_gnt": [{"direction": "in", "driver_value": "1"}], "m_axis_rx_tdata": [{"direction": "out", "size_left": "63", "size_right": "0"}], "m_axis_rx_tkeep": [{"direction": "out", "size_left": "7", "size_right": "0"}], "m_axis_rx_tlast": [{"direction": "out"}], "m_axis_rx_tvalid": [{"direction": "out"}], "m_axis_rx_tready": [{"direction": "in"}], "m_axis_rx_tuser": [{"direction": "out", "size_left": "21", "size_right": "0"}], "rx_np_ok": [{"direction": "in", "driver_value": "1"}], "rx_np_req": [{"direction": "in", "driver_value": "1"}], "cfg_mgmt_do": [{"direction": "out", "size_left": "31", "size_right": "0"}], "cfg_mgmt_rd_wr_done": [{"direction": "out"}], "cfg_status": [{"direction": "out", "size_left": "15", "size_right": "0"}], "cfg_command": [{"direction": "out", "size_left": "15", "size_right": "0"}], "cfg_dstatus": [{"direction": "out", "size_left": "15", "size_right": "0"}], "cfg_dcommand": [{"direction": "out", "size_left": "15", "size_right": "0"}], "cfg_lstatus": [{"direction": "out", "size_left": "15", "size_right": "0"}], "cfg_lcommand": [{"direction": "out", "size_left": "15", "size_right": "0"}], "cfg_dcommand2": [{"direction": "out", "size_left": "15", "size_right": "0"}], "cfg_pcie_link_state": [{"direction": "out", "size_left": "2", "size_right": "0"}], "cfg_pmcsr_pme_en": [{"direction": "out"}], "cfg_pmcsr_powerstate": [{"direction": "out", "size_left": "1", "size_right": "0"}], "cfg_pmcsr_pme_status": [{"direction": "out"}], "cfg_received_func_lvl_rst": [{"direction": "out"}], "cfg_mgmt_di": [{"direction": "in", "size_left": "31", "size_right": "0", "driver_value": "0"}], "cfg_mgmt_byte_en": [{"direction": "in", "size_left": "3", "size_right": "0", "driver_value": "0"}], "cfg_mgmt_dwaddr": [{"direction": "in", "size_left": "9", "size_right": "0", "driver_value": "0"}], "cfg_mgmt_wr_en": [{"direction": "in", "driver_value": "0"}], "cfg_mgmt_rd_en": [{"direction": "in", "driver_value": "0"}], "cfg_mgmt_wr_readonly": [{"direction": "in", "driver_value": "0"}], "cfg_trn_pending": [{"direction": "in", "driver_value": "0"}], "cfg_pm_halt_aspm_l0s": [{"direction": "in", "driver_value": "0"}], "cfg_pm_halt_aspm_l1": [{"direction": "in", "driver_value": "0"}], "cfg_pm_force_state_en": [{"direction": "in", "driver_value": "0"}], "cfg_pm_force_state": [{"direction": "in", "size_left": "1", "size_right": "0", "driver_value": "0"}], "cfg_dsn": [{"direction": "in", "size_left": "63", "size_right": "0", "driver_value": "0"}], "cfg_interrupt": [{"direction": "in", "driver_value": "0"}], "cfg_interrupt_rdy": [{"direction": "out"}], "cfg_interrupt_assert": [{"direction": "in", "driver_value": "0"}], "cfg_interrupt_di": [{"direction": "in", "size_left": "7", "size_right": "0", "driver_value": "0"}], "cfg_interrupt_do": [{"direction": "out", "size_left": "7", "size_right": "0"}], "cfg_interrupt_mmenable": [{"direction": "out", "size_left": "2", "size_right": "0"}], "cfg_interrupt_msienable": [{"direction": "out"}], "cfg_interrupt_msixenable": [{"direction": "out"}], "cfg_interrupt_msixfm": [{"direction": "out"}], "cfg_interrupt_stat": [{"direction": "in", "driver_value": "0"}], "cfg_pciecap_interrupt_msgnum": [{"direction": "in", "size_left": "4", "size_right": "0", "driver_value": "0"}], "cfg_to_turnoff": [{"direction": "out"}], "cfg_turnoff_ok": [{"direction": "in", "driver_value": "0"}], "cfg_bus_number": [{"direction": "out", "size_left": "7", "size_right": "0"}], "cfg_device_number": [{"direction": "out", "size_left": "4", "size_right": "0"}], "cfg_function_number": [{"direction": "out", "size_left": "2", "size_right": "0"}], "cfg_pm_wake": [{"direction": "in", "driver_value": "0"}], "cfg_pm_send_pme_to": [{"direction": "in", "driver_value": "0"}], "cfg_ds_bus_number": [{"direction": "in", "size_left": "7", "size_right": "0", "driver_value": "0"}], "cfg_ds_device_number": [{"direction": "in", "size_left": "4", "size_right": "0", "driver_value": "0"}], "cfg_ds_function_number": [{"direction": "in", "size_left": "2", "size_right": "0", "driver_value": "0"}], "cfg_mgmt_wr_rw1c_as_rw": [{"direction": "in", "driver_value": "0"}], "cfg_bridge_serr_en": [{"direction": "out"}], "cfg_slot_control_electromech_il_ctl_pulse": [{"direction": "out"}], "cfg_root_control_syserr_corr_err_en": [{"direction": "out"}], "cfg_root_control_syserr_non_fatal_err_en": [{"direction": "out"}], "cfg_root_control_syserr_fatal_err_en": [{"direction": "out"}], "cfg_root_control_pme_int_en": [{"direction": "out"}], "cfg_aer_rooterr_corr_err_reporting_en": [{"direction": "out"}], "cfg_aer_rooterr_non_fatal_err_reporting_en": [{"direction": "out"}], "cfg_aer_rooterr_fatal_err_reporting_en": [{"direction": "out"}], "cfg_aer_rooterr_corr_err_received": [{"direction": "out"}], "cfg_aer_rooterr_non_fatal_err_received": [{"direction": "out"}], "cfg_aer_rooterr_fatal_err_received": [{"direction": "out"}], "pl_directed_link_change": [{"direction": "in", "size_left": "1", "size_right": "0", "driver_value": "0"}], "pl_directed_link_width": [{"direction": "in", "size_left": "1", "size_right": "0", "driver_value": "0"}], "pl_directed_link_speed": [{"direction": "in", "driver_value": "0"}], "pl_directed_link_auton": [{"direction": "in", "driver_value": "0"}], "pl_upstream_prefer_deemph": [{"direction": "in", "driver_value": "1"}], "pl_sel_lnk_rate": [{"direction": "out"}], "pl_sel_lnk_width": [{"direction": "out", "size_left": "1", "size_right": "0"}], "pl_ltssm_state": [{"direction": "out", "size_left": "5", "size_right": "0"}], "pl_lane_reversal_mode": [{"direction": "out", "size_left": "1", "size_right": "0"}], "pl_phy_lnk_up": [{"direction": "out"}], "pl_tx_pm_state": [{"direction": "out", "size_left": "2", "size_right": "0"}], "pl_rx_pm_state": [{"direction": "out", "size_left": "1", "size_right": "0"}], "pl_link_upcfg_cap": [{"direction": "out"}], "pl_link_gen2_cap": [{"direction": "out"}], "pl_link_partner_gen2_supported": [{"direction": "out"}], "pl_initial_link_width": [{"direction": "out", "size_left": "2", "size_right": "0"}], "pl_directed_change_done": [{"direction": "out"}], "pl_received_hot_rst": [{"direction": "out"}], "pl_transmit_hot_rst": [{"direction": "in", "driver_value": "0"}], "pl_downstream_deemph_source": [{"direction": "in", "driver_value": "0"}], "cfg_vc_tcvc_map": [{"direction": "out", "size_left": "6", "size_right": "0"}], "sys_clk": [{"direction": "in"}], "sys_rst_n": [{"direction": "in"}], "pcie_drp_clk": [{"direction": "in", "driver_value": "1"}], "pcie_drp_en": [{"direction": "in", "driver_value": "0"}], "pcie_drp_we": [{"direction": "in", "driver_value": "0"}], "pcie_drp_addr": [{"direction": "in", "size_left": "8", "size_right": "0", "driver_value": "0"}], "pcie_drp_di": [{"direction": "in", "size_left": "15", "size_right": "0", "driver_value": "0"}], "pcie_drp_do": [{"direction": "out", "size_left": "15", "size_right": "0"}], "pcie_drp_rdy": [{"direction": "out"}]}, "interfaces": {"m_axis_rx": {"vlnv": "xilinx.com:interface:axis:1.0", "abstraction_type": "xilinx.com:interface:axis_rtl:1.0", "mode": "master", "parameters": {"TDATA_NUM_BYTES": [{"value": "8", "value_src": "auto", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "TDEST_WIDTH": [{"value": "0", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "TID_WIDTH": [{"value": "0", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "TUSER_WIDTH": [{"value": "22", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_TREADY": [{"value": "1", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_TSTRB": [{"value": "0", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_TKEEP": [{"value": "1", "value_src": "auto", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_TLAST": [{"value": "1", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_HZ": [{"value": "100000000", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "LAYERED_METADATA": [{"value": "undef", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"TDATA": [{"physical_name": "m_axis_rx_tdata"}], "TKEEP": [{"physical_name": "m_axis_rx_tkeep"}], "TLAST": [{"physical_name": "m_axis_rx_tlast"}], "TREADY": [{"physical_name": "m_axis_rx_tready"}], "TUSER": [{"physical_name": "m_axis_rx_tuser"}], "TVALID": [{"physical_name": "m_axis_rx_tvalid"}]}}, "s_axis_tx": {"vlnv": "xilinx.com:interface:axis:1.0", "abstraction_type": "xilinx.com:interface:axis_rtl:1.0", "mode": "slave", "parameters": {"TDATA_NUM_BYTES": [{"value": "8", "value_src": "auto", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "TDEST_WIDTH": [{"value": "0", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "TID_WIDTH": [{"value": "0", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "TUSER_WIDTH": [{"value": "4", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_TREADY": [{"value": "1", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_TSTRB": [{"value": "0", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_TKEEP": [{"value": "1", "value_src": "auto", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "HAS_TLAST": [{"value": "1", "value_src": "constant", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_HZ": [{"value": "100000000", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "LAYERED_METADATA": [{"value": "undef", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"TDATA": [{"physical_name": "s_axis_tx_tdata"}], "TKEEP": [{"physical_name": "s_axis_tx_tkeep"}], "TLAST": [{"physical_name": "s_axis_tx_tlast"}], "TREADY": [{"physical_name": "s_axis_tx_tready"}], "TUSER": [{"physical_name": "s_axis_tx_tuser"}], "TVALID": [{"physical_name": "s_axis_tx_tvalid"}]}}, "pcie_7x_mgt": {"vlnv": "xilinx.com:interface:pcie_7x_mgt:1.0", "abstraction_type": "xilinx.com:interface:pcie_7x_mgt_rtl:1.0", "mode": "master", "port_maps": {"rxn": [{"physical_name": "pci_exp_rxn"}], "rxp": [{"physical_name": "pci_exp_rxp"}], "txn": [{"physical_name": "pci_exp_txn"}], "txp": [{"physical_name": "pci_exp_txp"}]}}, "drp": {"vlnv": "xilinx.com:interface:drp:1.0", "abstraction_type": "xilinx.com:interface:drp_rtl:1.0", "mode": "slave", "port_maps": {"DADDR": [{"physical_name": "pcie_drp_addr"}], "DEN": [{"physical_name": "pcie_drp_en"}], "DI": [{"physical_name": "pcie_drp_di"}], "DO": [{"physical_name": "pcie_drp_do"}], "DRDY": [{"physical_name": "pcie_drp_rdy"}], "DWE": [{"physical_name": "pcie_drp_we"}]}}, "CLK.sys_clk": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "slave", "parameters": {"FREQ_HZ": [{"value": "100000000", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_BUSIF": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK": [{"physical_name": "sys_clk"}]}}, "CLK.user_clk_out": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "master", "parameters": {"ASSOCIATED_BUSIF": [{"value": "m_axis_rx:s_axis_tx", "value_src": "constant", "usage": "all"}], "FREQ_HZ": [{"value": "125000000", "value_src": "constant", "usage": "all"}], "ASSOCIATED_RESET": [{"value": "user_reset_out", "value_src": "constant", "usage": "all"}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK": [{"physical_name": "user_clk_out"}]}}, "RST.sys_rst_n": {"vlnv": "xilinx.com:signal:reset:1.0", "abstraction_type": "xilinx.com:signal:reset_rtl:1.0", "mode": "slave", "parameters": {"POLARITY": [{"value": "ACTIVE_LOW", "value_src": "constant", "usage": "all"}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"RST": [{"physical_name": "sys_rst_n"}]}}, "RST.user_reset_out": {"vlnv": "xilinx.com:signal:reset:1.0", "abstraction_type": "xilinx.com:signal:reset_rtl:1.0", "mode": "master", "parameters": {"POLARITY": [{"value": "ACTIVE_HIGH", "value_src": "constant", "usage": "all"}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"RST": [{"physical_name": "user_reset_out"}]}}, "pcie2_cfg_interrupt": {"vlnv": "xilinx.com:interface:pcie2_cfg_interrupt:1.0", "abstraction_type": "xilinx.com:interface:pcie2_cfg_interrupt_rtl:1.0", "mode": "slave", "port_maps": {"assert": [{"physical_name": "cfg_interrupt_assert"}], "interrupt": [{"physical_name": "cfg_interrupt"}], "mmenable": [{"physical_name": "cfg_interrupt_mmenable"}], "msienable": [{"physical_name": "cfg_interrupt_msienable"}], "msixenable": [{"physical_name": "cfg_interrupt_msixenable"}], "msixfm": [{"physical_name": "cfg_interrupt_msixfm"}], "pciecap_interrupt_msgnum": [{"physical_name": "cfg_pciecap_interrupt_msgnum"}], "rdy": [{"physical_name": "cfg_interrupt_rdy"}], "read_data": [{"physical_name": "cfg_interrupt_do"}], "stat": [{"physical_name": "cfg_interrupt_stat"}], "write_data": [{"physical_name": "cfg_interrupt_di"}]}}, "pcie2_cfg_status": {"vlnv": "xilinx.com:interface:pcie2_cfg_status:1.0", "abstraction_type": "xilinx.com:interface:pcie2_cfg_status_rtl:1.0", "mode": "master", "port_maps": {"aer_rooterr_corr_err_received": [{"physical_name": "cfg_aer_rooterr_corr_err_received"}], "aer_rooterr_corr_err_reporting_en": [{"physical_name": "cfg_aer_rooterr_corr_err_reporting_en"}], "aer_rooterr_fatal_err_received": [{"physical_name": "cfg_aer_rooterr_fatal_err_received"}], "aer_rooterr_fatal_err_reporting_en": [{"physical_name": "cfg_aer_rooterr_fatal_err_reporting_en"}], "aer_rooterr_non_fatal_err_received": [{"physical_name": "cfg_aer_rooterr_non_fatal_err_received"}], "aer_rooterr_non_fatal_err_reporting_en": [{"physical_name": "cfg_aer_rooterr_non_fatal_err_reporting_en"}], "bridge_serr_en": [{"physical_name": "cfg_bridge_serr_en"}], "bus_number": [{"physical_name": "cfg_bus_number"}], "command": [{"physical_name": "cfg_command"}], "dcommand": [{"physical_name": "cfg_dcommand"}], "dcommand2": [{"physical_name": "cfg_dcommand2"}], "device_number": [{"physical_name": "cfg_device_number"}], "dstatus": [{"physical_name": "cfg_dstatus"}], "function_number": [{"physical_name": "cfg_function_number"}], "lcommand": [{"physical_name": "cfg_lcommand"}], "lstatus": [{"physical_name": "cfg_lstatus"}], "pcie_link_state": [{"physical_name": "cfg_pcie_link_state"}], "pmcsr_pme_en": [{"physical_name": "cfg_pmcsr_pme_en"}], "pmcsr_pme_status": [{"physical_name": "cfg_pmcsr_pme_status"}], "pmcsr_powerstate": [{"physical_name": "cfg_pmcsr_powerstate"}], "received_func_lvl_rst": [{"physical_name": "cfg_received_func_lvl_rst"}], "root_control_pme_int_en": [{"physical_name": "cfg_root_control_pme_int_en"}], "root_control_syserr_corr_err_en": [{"physical_name": "cfg_root_control_syserr_corr_err_en"}], "root_control_syserr_fatal_err_en": [{"physical_name": "cfg_root_control_syserr_fatal_err_en"}], "root_control_syserr_non_fatal_err_en": [{"physical_name": "cfg_root_control_syserr_non_fatal_err_en"}], "slot_control_electromech_il_ctl_pulse": [{"physical_name": "cfg_slot_control_electromech_il_ctl_pulse"}], "status": [{"physical_name": "cfg_status"}], "turnoff": [{"physical_name": "cfg_to_turnoff"}], "tx_buf_av": [{"physical_name": "tx_buf_av"}], "tx_cfg_req": [{"physical_name": "tx_cfg_req"}], "tx_err_drop": [{"physical_name": "tx_err_drop"}], "vc_tcvc_map": [{"physical_name": "cfg_vc_tcvc_map"}]}}, "pcie2_cfg_control": {"vlnv": "xilinx.com:interface:pcie2_cfg_control:1.0", "abstraction_type": "xilinx.com:interface:pcie2_cfg_control_rtl:1.0", "mode": "slave", "port_maps": {"ds_bus_number": [{"physical_name": "cfg_ds_bus_number"}], "ds_device_number": [{"physical_name": "cfg_ds_device_number"}], "ds_function_number": [{"physical_name": "cfg_ds_function_number"}], "dsn": [{"physical_name": "cfg_dsn"}], "pm_force_state": [{"physical_name": "cfg_pm_force_state"}], "pm_force_state_en": [{"physical_name": "cfg_pm_force_state_en"}], "pm_halt_aspm_l0s": [{"physical_name": "cfg_pm_halt_aspm_l0s"}], "pm_halt_aspm_l1": [{"physical_name": "cfg_pm_halt_aspm_l1"}], "pm_send_pme_to": [{"physical_name": "cfg_pm_send_pme_to"}], "pm_wake": [{"physical_name": "cfg_pm_wake"}], "rx_np_ok": [{"physical_name": "rx_np_ok"}], "rx_np_req": [{"physical_name": "rx_np_req"}], "trn_pending": [{"physical_name": "cfg_trn_pending"}], "turnoff_ok": [{"physical_name": "cfg_turnoff_ok"}], "tx_cfg_gnt": [{"physical_name": "tx_cfg_gnt"}]}}, "pcie_cfg_mgmt": {"vlnv": "xilinx.com:interface:pcie_cfg_mgmt:1.0", "abstraction_type": "xilinx.com:interface:pcie_cfg_mgmt_rtl:1.0", "mode": "slave", "port_maps": {"ADDR": [{"physical_name": "cfg_mgmt_dwaddr"}], "BYTE_EN": [{"physical_name": "cfg_mgmt_byte_en"}], "READ_DATA": [{"physical_name": "cfg_mgmt_do"}], "READ_EN": [{"physical_name": "cfg_mgmt_rd_en"}], "READ_WRITE_DONE": [{"physical_name": "cfg_mgmt_rd_wr_done"}], "READONLY": [{"physical_name": "cfg_mgmt_wr_readonly"}], "TYPE1_CFG_REG_ACCESS": [{"physical_name": "cfg_mgmt_wr_rw1c_as_rw"}], "WRITE_DATA": [{"physical_name": "cfg_mgmt_di"}], "WRITE_EN": [{"physical_name": "cfg_mgmt_wr_en"}]}}, "pcie2_pl": {"vlnv": "xilinx.com:interface:pcie2_pl:1.0", "abstraction_type": "xilinx.com:interface:pcie2_pl_rtl:1.0", "mode": "slave", "port_maps": {"directed_change_done": [{"physical_name": "pl_directed_change_done"}], "directed_link_auton": [{"physical_name": "pl_directed_link_auton"}], "directed_link_change": [{"physical_name": "pl_directed_link_change"}], "directed_link_speed": [{"physical_name": "pl_directed_link_speed"}], "directed_link_width": [{"physical_name": "pl_directed_link_width"}], "downstream_deemph_source": [{"physical_name": "pl_downstream_deemph_source"}], "initial_link_width": [{"physical_name": "pl_initial_link_width"}], "lane_reversal_mode": [{"physical_name": "pl_lane_reversal_mode"}], "link_gen2_cap": [{"physical_name": "pl_link_gen2_cap"}], "link_partner_gen2_supported": [{"physical_name": "pl_link_partner_gen2_supported"}], "link_upcfg_cap": [{"physical_name": "pl_link_upcfg_cap"}], "ltssm_state": [{"physical_name": "pl_ltssm_state"}], "phy_lnk_up": [{"physical_name": "pl_phy_lnk_up"}], "received_hot_rst": [{"physical_name": "pl_received_hot_rst"}], "rx_pm_state": [{"physical_name": "pl_rx_pm_state"}], "sel_lnk_rate": [{"physical_name": "pl_sel_lnk_rate"}], "sel_lnk_width": [{"physical_name": "pl_sel_lnk_width"}], "transmit_hot_rst": [{"physical_name": "pl_transmit_hot_rst"}], "tx_pm_state": [{"physical_name": "pl_tx_pm_state"}], "upstream_prefer_deemph": [{"physical_name": "pl_upstream_prefer_deemph"}]}}}}}}