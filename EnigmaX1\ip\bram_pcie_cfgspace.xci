{"schema": "xilinx.com:schema:json_instance:1.0", "ip_inst": {"xci_name": "bram_pcie_cfgspace", "component_reference": "xilinx.com:ip:blk_mem_gen:8.4", "ip_revision": "7", "gen_directory": "../../../../pcileech_enigma_x1.gen/sources_1/ip/bram_pcie_cfgspace", "parameters": {"component_parameters": {"Component_Name": [{"value": "bram_pcie_cfgspace", "resolve_type": "user", "usage": "all"}], "Interface_Type": [{"value": "Native", "resolve_type": "user", "usage": "all"}], "AXI_Type": [{"value": "AXI4_Full", "resolve_type": "user", "usage": "all"}], "AXI_Slave_Type": [{"value": "Memory_Slave", "resolve_type": "user", "usage": "all"}], "Use_AXI_ID": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "AXI_ID_Width": [{"value": "4", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Memory_Type": [{"value": "Simple_Dual_Port_RAM", "value_src": "user", "resolve_type": "user", "usage": "all"}], "PRIM_type_to_Implement": [{"value": "BRAM", "resolve_type": "user", "usage": "all"}], "Enable_32bit_Address": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "ecctype": [{"value": "No_ECC", "resolve_type": "user", "usage": "all"}], "ECC": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "softecc": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "EN_SLEEP_PIN": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "EN_DEEPSLEEP_PIN": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "EN_SHUTDOWN_PIN": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "EN_ECC_PIPE": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "RD_ADDR_CHNG_A": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "RD_ADDR_CHNG_B": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Use_Error_Injection_Pins": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Error_Injection_Type": [{"value": "Single_Bit_Error_Injection", "resolve_type": "user", "enabled": false, "usage": "all"}], "Use_Byte_Write_Enable": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "Byte_Size": [{"value": "8", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Algorithm": [{"value": "Minimum_Area", "resolve_type": "user", "usage": "all"}], "Primitive": [{"value": "8kx2", "resolve_type": "user", "enabled": false, "usage": "all"}], "Assume_Synchronous_Clk": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "Write_Width_A": [{"value": "32", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "Write_Depth_A": [{"value": "1024", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "Read_Width_A": [{"value": "32", "value_src": "user", "resolve_type": "user", "enabled": false, "usage": "all"}], "Operating_Mode_A": [{"value": "WRITE_FIRST", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Enable_A": [{"value": "Always_Enabled", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Write_Width_B": [{"value": "32", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Read_Width_B": [{"value": "32", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Operating_Mode_B": [{"value": "READ_FIRST", "value_src": "user", "resolve_type": "user", "enabled": false, "usage": "all"}], "Enable_B": [{"value": "Always_Enabled", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Register_PortA_Output_of_Memory_Primitives": [{"value": "false", "value_src": "user", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Register_PortA_Output_of_Memory_Core": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Use_REGCEA_Pin": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Register_PortB_Output_of_Memory_Primitives": [{"value": "false", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "Register_PortB_Output_of_Memory_Core": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Use_REGCEB_Pin": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "register_porta_input_of_softecc": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "register_portb_output_of_softecc": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Pipeline_Stages": [{"value": "0", "resolve_type": "user", "enabled": false, "usage": "all"}], "Load_Init_File": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "Coe_File": [{"value": "pcileech_cfgspace.coe", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Fill_Remaining_Memory_Locations": [{"value": "false", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "Remaining_Memory_Locations": [{"value": "0", "value_src": "user", "resolve_type": "user", "enabled": false, "usage": "all"}], "Use_RSTA_Pin": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Reset_Memory_Latch_A": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Reset_Priority_A": [{"value": "CE", "resolve_type": "user", "enabled": false, "usage": "all"}], "Output_Reset_Value_A": [{"value": "0", "resolve_type": "user", "enabled": false, "usage": "all"}], "Use_RSTB_Pin": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Reset_Memory_Latch_B": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Reset_Priority_B": [{"value": "CE", "resolve_type": "user", "enabled": false, "usage": "all"}], "Output_Reset_Value_B": [{"value": "0", "resolve_type": "user", "enabled": false, "usage": "all"}], "Reset_Type": [{"value": "SYNC", "resolve_type": "user", "enabled": false, "usage": "all"}], "Additional_Inputs_for_Power_Estimation": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Port_A_Clock": [{"value": "100", "resolve_type": "user", "format": "long", "usage": "all"}], "Port_A_Write_Rate": [{"value": "50", "resolve_type": "user", "format": "long", "usage": "all"}], "Port_B_Clock": [{"value": "100", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "Port_B_Write_Rate": [{"value": "0", "value_src": "user", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Port_A_Enable_Rate": [{"value": "100", "resolve_type": "user", "format": "long", "usage": "all"}], "Port_B_Enable_Rate": [{"value": "100", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "Collision_Warnings": [{"value": "ALL", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Disable_Collision_Warnings": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Disable_Out_of_Range_Warnings": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "use_bram_block": [{"value": "Stand_Alone", "resolve_type": "user", "usage": "all"}], "MEM_FILE": [{"value": "no_mem_loaded", "resolve_type": "user", "usage": "all"}], "CTRL_ECC_ALGO": [{"value": "NONE", "resolve_type": "user", "usage": "all"}], "EN_SAFETY_CKT": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "READ_LATENCY_A": [{"value": "1", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "READ_LATENCY_B": [{"value": "1", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}]}, "model_parameters": {"C_FAMILY": [{"value": "artix7", "resolve_type": "generated", "usage": "all"}], "C_XDEVICEFAMILY": [{"value": "artix7", "resolve_type": "generated", "usage": "all"}], "C_ELABORATION_DIR": [{"value": "./", "resolve_type": "generated", "usage": "all"}], "C_INTERFACE_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_TYPE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_SLAVE_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_BRAM_BLOCK": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ENABLE_32BIT_ADDRESS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_CTRL_ECC_ALGO": [{"value": "NONE", "resolve_type": "generated", "usage": "all"}], "C_HAS_AXI_ID": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_ID_WIDTH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MEM_TYPE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_BYTE_SIZE": [{"value": "8", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ALGORITHM": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PRIM_TYPE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_LOAD_INIT_FILE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_INIT_FILE_NAME": [{"value": "bram_pcie_cfgspace.mif", "resolve_type": "generated", "usage": "all"}], "C_INIT_FILE": [{"value": "bram_pcie_cfgspace.mem", "resolve_type": "generated", "usage": "all"}], "C_USE_DEFAULT_DATA": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DEFAULT_DATA": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "C_HAS_RSTA": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_RST_PRIORITY_A": [{"value": "CE", "resolve_type": "generated", "usage": "all"}], "C_RSTRAM_A": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_INITA_VAL": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "C_HAS_ENA": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_REGCEA": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_BYTE_WEA": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WEA_WIDTH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WRITE_MODE_A": [{"value": "WRITE_FIRST", "resolve_type": "generated", "usage": "all"}], "C_WRITE_WIDTH_A": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_READ_WIDTH_A": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WRITE_DEPTH_A": [{"value": "1024", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_READ_DEPTH_A": [{"value": "1024", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ADDRA_WIDTH": [{"value": "10", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_RSTB": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_RST_PRIORITY_B": [{"value": "CE", "resolve_type": "generated", "usage": "all"}], "C_RSTRAM_B": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_INITB_VAL": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "C_HAS_ENB": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_REGCEB": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_BYTE_WEB": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WEB_WIDTH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WRITE_MODE_B": [{"value": "READ_FIRST", "resolve_type": "generated", "usage": "all"}], "C_WRITE_WIDTH_B": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_READ_WIDTH_B": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WRITE_DEPTH_B": [{"value": "1024", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_READ_DEPTH_B": [{"value": "1024", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ADDRB_WIDTH": [{"value": "10", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_MEM_OUTPUT_REGS_A": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_MEM_OUTPUT_REGS_B": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_MUX_OUTPUT_REGS_A": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_MUX_OUTPUT_REGS_B": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MUX_PIPELINE_STAGES": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_SOFTECC_INPUT_REGS_A": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_SOFTECC_OUTPUT_REGS_B": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_SOFTECC": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_ECC": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_EN_ECC_PIPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_READ_LATENCY_A": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_READ_LATENCY_B": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_INJECTERR": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SIM_COLLISION_CHECK": [{"value": "ALL", "resolve_type": "generated", "usage": "all"}], "C_COMMON_CLK": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DISABLE_WARN_BHV_COLL": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_EN_SLEEP_PIN": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_URAM": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_EN_RDADDRA_CHG": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_EN_RDADDRB_CHG": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_EN_DEEPSLEEP_PIN": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_EN_SHUTDOWN_PIN": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_EN_SAFETY_CKT": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DISABLE_WARN_BHV_RANGE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_COUNT_36K_BRAM": [{"value": "1", "resolve_type": "generated", "usage": "all"}], "C_COUNT_18K_BRAM": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "C_EST_POWER_SUMMARY": [{"value": "Estimated Power for IP     :     5.7864 mW", "resolve_type": "generated", "usage": "all"}]}, "project_parameters": {"ARCHITECTURE": [{"value": "artix7"}], "BASE_BOARD_PART": [{"value": ""}], "BOARD_CONNECTIONS": [{"value": ""}], "DEVICE": [{"value": "xc7a75t"}], "PACKAGE": [{"value": "fgg484"}], "PREFHDL": [{"value": "VERILOG"}], "SILICON_REVISION": [{"value": ""}], "SIMULATOR_LANGUAGE": [{"value": "MIXED"}], "SPEEDGRADE": [{"value": "-2"}], "STATIC_POWER": [{"value": ""}], "TEMPERATURE_GRADE": [{"value": ""}], "USE_RDI_CUSTOMIZATION": [{"value": "TRUE"}], "USE_RDI_GENERATION": [{"value": "TRUE"}]}, "runtime_parameters": {"IPCONTEXT": [{"value": "IP_Flow"}], "IPREVISION": [{"value": "7"}], "MANAGED": [{"value": "TRUE"}], "OUTPUTDIR": [{"value": "../../../../pcileech_enigma_x1.gen/sources_1/ip/bram_pcie_cfgspace"}], "SELECTEDSIMMODEL": [{"value": ""}], "SHAREDDIR": [{"value": "."}], "SWVERSION": [{"value": "2023.2"}], "SYNTHESISFLOW": [{"value": "OUT_OF_CONTEXT"}]}}, "boundary": {"ports": {"clka": [{"direction": "in", "driver_value": "0"}], "wea": [{"direction": "in", "size_left": "3", "size_right": "0", "driver_value": "0"}], "addra": [{"direction": "in", "size_left": "9", "size_right": "0", "driver_value": "0"}], "dina": [{"direction": "in", "size_left": "31", "size_right": "0", "driver_value": "0"}], "clkb": [{"direction": "in", "driver_value": "0"}], "addrb": [{"direction": "in", "size_left": "9", "size_right": "0", "driver_value": "0"}], "doutb": [{"direction": "out", "size_left": "31", "size_right": "0"}]}, "interfaces": {"CLK.ACLK": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "slave", "parameters": {"ASSOCIATED_BUSIF": [{"value": "AXI_SLAVE_S_AXI:AXILite_SLAVE_S_AXI", "value_src": "constant", "usage": "all"}], "ASSOCIATED_RESET": [{"value": "s_aresetn", "value_src": "constant", "usage": "all"}], "FREQ_HZ": [{"value": "100000000", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}}, "RST.ARESETN": {"vlnv": "xilinx.com:signal:reset:1.0", "abstraction_type": "xilinx.com:signal:reset_rtl:1.0", "mode": "slave", "parameters": {"POLARITY": [{"value": "ACTIVE_LOW", "value_src": "constant", "usage": "all"}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}}, "BRAM_PORTA": {"vlnv": "xilinx.com:interface:bram:1.0", "abstraction_type": "xilinx.com:interface:bram_rtl:1.0", "mode": "slave", "parameters": {"MEM_SIZE": [{"value": "8192", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "MEM_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "MEM_ECC": [{"value": "NONE", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "MASTER_TYPE": [{"value": "OTHER", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "READ_WRITE_MODE": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "READ_LATENCY": [{"value": "1", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"ADDR": [{"physical_name": "addra"}], "CLK": [{"physical_name": "clka"}], "DIN": [{"physical_name": "dina"}], "WE": [{"physical_name": "wea"}]}}, "BRAM_PORTB": {"vlnv": "xilinx.com:interface:bram:1.0", "abstraction_type": "xilinx.com:interface:bram_rtl:1.0", "mode": "slave", "parameters": {"MEM_SIZE": [{"value": "8192", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "MEM_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "MEM_ECC": [{"value": "NONE", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "MASTER_TYPE": [{"value": "OTHER", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "READ_WRITE_MODE": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "READ_LATENCY": [{"value": "1", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"ADDR": [{"physical_name": "addrb"}], "CLK": [{"physical_name": "clkb"}], "DOUT": [{"physical_name": "doutb"}]}}}, "memory_maps": {"S_1": {"address_blocks": {"Mem0": {"base_address": "0", "range": "4096", "usage": "memory", "access": "read-write", "parameters": {"OFFSET_BASE_PARAM": [{"value": "C_BASEADDR"}], "OFFSET_HIGH_PARAM": [{"value": "C_HIGHADDR"}]}}}}}}}}