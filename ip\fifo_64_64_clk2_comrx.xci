{"schema": "xilinx.com:schema:json_instance:1.0", "ip_inst": {"xci_name": "fifo_64_64_clk2_comrx", "component_reference": "xilinx.com:ip:fifo_generator:13.2", "ip_revision": "9", "gen_directory": "../../../../pcileech_enigma_x1.gen/sources_1/ip/fifo_64_64_clk2_comrx", "parameters": {"component_parameters": {"Component_Name": [{"value": "fifo_64_64_clk2_comrx", "resolve_type": "user", "usage": "all"}], "Fifo_Implementation": [{"value": "Independent_Clocks_Distributed_RAM", "value_src": "user", "resolve_type": "user", "usage": "all"}], "synchronization_stages": [{"value": "2", "resolve_type": "user", "format": "long", "usage": "all"}], "synchronization_stages_axi": [{"value": "2", "resolve_type": "user", "format": "long", "usage": "all"}], "INTERFACE_TYPE": [{"value": "Native", "resolve_type": "user", "usage": "all"}], "Performance_Options": [{"value": "Standard_FIFO", "resolve_type": "user", "usage": "all"}], "asymmetric_port_width": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Input_Data_Width": [{"value": "64", "value_src": "user", "resolve_type": "user", "format": "long", "usage": "all"}], "Input_Depth": [{"value": "64", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Output_Data_Width": [{"value": "64", "value_src": "user", "resolve_type": "user", "enabled": false, "usage": "all"}], "Output_Depth": [{"value": "64", "value_src": "user", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Enable_ECC": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Use_Embedded_Registers": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Reset_Pin": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Enable_Reset_Synchronization": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Reset_Type": [{"value": "Asynchronous_Reset", "value_src": "user", "resolve_type": "user", "enabled": false, "usage": "all"}], "Full_Flags_Reset_Value": [{"value": "1", "value_src": "user", "resolve_type": "user", "usage": "all"}], "Use_Dout_Reset": [{"value": "true", "resolve_type": "user", "format": "bool", "usage": "all"}], "Dout_Reset_Value": [{"value": "0", "resolve_type": "user", "usage": "all"}], "dynamic_power_saving": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Almost_Full_Flag": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Almost_Empty_Flag": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Valid_Flag": [{"value": "true", "value_src": "user", "resolve_type": "user", "format": "bool", "usage": "all"}], "Valid_Sense": [{"value": "Active_High", "resolve_type": "user", "usage": "all"}], "Underflow_Flag": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Underflow_Sense": [{"value": "Active_High", "resolve_type": "user", "enabled": false, "usage": "all"}], "Write_Acknowledge_Flag": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Write_Acknowledge_Sense": [{"value": "Active_High", "resolve_type": "user", "enabled": false, "usage": "all"}], "Overflow_Flag": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Overflow_Sense": [{"value": "Active_High", "resolve_type": "user", "enabled": false, "usage": "all"}], "Inject_Sbit_Error": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Dbit_Error": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "ecc_pipeline_reg": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Use_Extra_Logic": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Data_Count": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Data_Count_Width": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Write_Data_Count": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Write_Data_Count_Width": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Read_Data_Count": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Read_Data_Count_Width": [{"value": "4", "value_src": "user", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Disable_Timing_Violations": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Read_Clock_Frequency": [{"value": "1", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Write_Clock_Frequency": [{"value": "1", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Programmable_Full_Type": [{"value": "No_Programmable_Full_Threshold", "resolve_type": "user", "usage": "all"}], "Full_Threshold_Assert_Value": [{"value": "13", "value_src": "user", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Full_Threshold_Negate_Value": [{"value": "12", "value_src": "user", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Programmable_Empty_Type": [{"value": "No_Programmable_Empty_Threshold", "resolve_type": "user", "usage": "all"}], "Empty_Threshold_Assert_Value": [{"value": "2", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Empty_Threshold_Negate_Value": [{"value": "3", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "PROTOCOL": [{"value": "AXI4", "resolve_type": "user", "enabled": false, "usage": "all"}], "Clock_Type_AXI": [{"value": "Common_Clock", "resolve_type": "user", "usage": "all"}], "HAS_ACLKEN": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Clock_Enable_Type": [{"value": "Slave_Interface_Clock_Enable", "resolve_type": "user", "enabled": false, "usage": "all"}], "READ_WRITE_MODE": [{"value": "READ_WRITE", "resolve_type": "user", "usage": "all"}], "ID_WIDTH": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "ADDRESS_WIDTH": [{"value": "32", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "DATA_WIDTH": [{"value": "64", "resolve_type": "user", "enabled": false, "usage": "all"}], "AWUSER_Width": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "WUSER_Width": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "BUSER_Width": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "ARUSER_Width": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "RUSER_Width": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "TDATA_NUM_BYTES": [{"value": "1", "resolve_type": "user", "usage": "all"}], "TID_WIDTH": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "TDEST_WIDTH": [{"value": "0", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "TUSER_WIDTH": [{"value": "4", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Enable_TREADY": [{"value": "true", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Enable_TLAST": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "HAS_TSTRB": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "TSTRB_WIDTH": [{"value": "1", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "HAS_TKEEP": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "TKEEP_WIDTH": [{"value": "1", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "wach_type": [{"value": "FIFO", "resolve_type": "user", "usage": "all"}], "FIFO_Implementation_wach": [{"value": "Common_Clock_Block_RAM", "resolve_type": "user", "usage": "all"}], "FIFO_Application_Type_wach": [{"value": "Data_FIFO", "resolve_type": "user", "enabled": false, "usage": "all"}], "Enable_ECC_wach": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Sbit_Error_wach": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Dbit_Error_wach": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Input_Depth_wach": [{"value": "16", "resolve_type": "user", "usage": "all"}], "Enable_Data_Counts_wach": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Programmable_Full_Type_wach": [{"value": "No_Programmable_Full_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Full_Threshold_Assert_Value_wach": [{"value": "1023", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Programmable_Empty_Type_wach": [{"value": "No_Programmable_Empty_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Empty_Threshold_Assert_Value_wach": [{"value": "1022", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "wdch_type": [{"value": "FIFO", "resolve_type": "user", "usage": "all"}], "FIFO_Implementation_wdch": [{"value": "Common_Clock_Block_RAM", "resolve_type": "user", "usage": "all"}], "FIFO_Application_Type_wdch": [{"value": "Data_FIFO", "resolve_type": "user", "enabled": false, "usage": "all"}], "Enable_ECC_wdch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Sbit_Error_wdch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Dbit_Error_wdch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Input_Depth_wdch": [{"value": "1024", "resolve_type": "user", "usage": "all"}], "Enable_Data_Counts_wdch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Programmable_Full_Type_wdch": [{"value": "No_Programmable_Full_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Full_Threshold_Assert_Value_wdch": [{"value": "1023", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Programmable_Empty_Type_wdch": [{"value": "No_Programmable_Empty_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Empty_Threshold_Assert_Value_wdch": [{"value": "1022", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "wrch_type": [{"value": "FIFO", "resolve_type": "user", "usage": "all"}], "FIFO_Implementation_wrch": [{"value": "Common_Clock_Block_RAM", "resolve_type": "user", "usage": "all"}], "FIFO_Application_Type_wrch": [{"value": "Data_FIFO", "resolve_type": "user", "enabled": false, "usage": "all"}], "Enable_ECC_wrch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Sbit_Error_wrch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Dbit_Error_wrch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Input_Depth_wrch": [{"value": "16", "resolve_type": "user", "usage": "all"}], "Enable_Data_Counts_wrch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Programmable_Full_Type_wrch": [{"value": "No_Programmable_Full_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Full_Threshold_Assert_Value_wrch": [{"value": "1023", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Programmable_Empty_Type_wrch": [{"value": "No_Programmable_Empty_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Empty_Threshold_Assert_Value_wrch": [{"value": "1022", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "rach_type": [{"value": "FIFO", "resolve_type": "user", "usage": "all"}], "FIFO_Implementation_rach": [{"value": "Common_Clock_Block_RAM", "resolve_type": "user", "usage": "all"}], "FIFO_Application_Type_rach": [{"value": "Data_FIFO", "resolve_type": "user", "enabled": false, "usage": "all"}], "Enable_ECC_rach": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Sbit_Error_rach": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Dbit_Error_rach": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Input_Depth_rach": [{"value": "16", "resolve_type": "user", "usage": "all"}], "Enable_Data_Counts_rach": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Programmable_Full_Type_rach": [{"value": "No_Programmable_Full_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Full_Threshold_Assert_Value_rach": [{"value": "1023", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Programmable_Empty_Type_rach": [{"value": "No_Programmable_Empty_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Empty_Threshold_Assert_Value_rach": [{"value": "1022", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "rdch_type": [{"value": "FIFO", "resolve_type": "user", "usage": "all"}], "FIFO_Implementation_rdch": [{"value": "Common_Clock_Block_RAM", "resolve_type": "user", "usage": "all"}], "FIFO_Application_Type_rdch": [{"value": "Data_FIFO", "resolve_type": "user", "enabled": false, "usage": "all"}], "Enable_ECC_rdch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Sbit_Error_rdch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Dbit_Error_rdch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Input_Depth_rdch": [{"value": "1024", "resolve_type": "user", "usage": "all"}], "Enable_Data_Counts_rdch": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Programmable_Full_Type_rdch": [{"value": "No_Programmable_Full_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Full_Threshold_Assert_Value_rdch": [{"value": "1023", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Programmable_Empty_Type_rdch": [{"value": "No_Programmable_Empty_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Empty_Threshold_Assert_Value_rdch": [{"value": "1022", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "axis_type": [{"value": "FIFO", "resolve_type": "user", "usage": "all"}], "FIFO_Implementation_axis": [{"value": "Common_Clock_Block_RAM", "resolve_type": "user", "usage": "all"}], "FIFO_Application_Type_axis": [{"value": "Data_FIFO", "resolve_type": "user", "enabled": false, "usage": "all"}], "Enable_ECC_axis": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Sbit_Error_axis": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Inject_Dbit_Error_axis": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Input_Depth_axis": [{"value": "1024", "resolve_type": "user", "usage": "all"}], "Enable_Data_Counts_axis": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Programmable_Full_Type_axis": [{"value": "No_Programmable_Full_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Full_Threshold_Assert_Value_axis": [{"value": "1023", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Programmable_Empty_Type_axis": [{"value": "No_Programmable_Empty_Threshold", "resolve_type": "user", "enabled": false, "usage": "all"}], "Empty_Threshold_Assert_Value_axis": [{"value": "1022", "resolve_type": "user", "format": "long", "enabled": false, "usage": "all"}], "Register_Slice_Mode_wach": [{"value": "Fully_Registered", "resolve_type": "user", "usage": "all"}], "Register_Slice_Mode_wdch": [{"value": "Fully_Registered", "resolve_type": "user", "usage": "all"}], "Register_Slice_Mode_wrch": [{"value": "Fully_Registered", "resolve_type": "user", "usage": "all"}], "Register_Slice_Mode_rach": [{"value": "Fully_Registered", "resolve_type": "user", "usage": "all"}], "Register_Slice_Mode_rdch": [{"value": "Fully_Registered", "resolve_type": "user", "usage": "all"}], "Register_Slice_Mode_axis": [{"value": "Fully_Registered", "resolve_type": "user", "usage": "all"}], "Underflow_Flag_AXI": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Underflow_Sense_AXI": [{"value": "Active_High", "resolve_type": "user", "usage": "all"}], "Overflow_Flag_AXI": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Overflow_Sense_AXI": [{"value": "Active_High", "resolve_type": "user", "usage": "all"}], "Disable_Timing_Violations_AXI": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Add_NGC_Constraint_AXI": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Enable_Common_Underflow": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Enable_Common_Overflow": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "enable_read_pointer_increment_by2": [{"value": "false", "resolve_type": "user", "format": "bool", "usage": "all"}], "Use_Embedded_Registers_axis": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "enable_low_latency": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "use_dout_register": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Master_interface_Clock_enable_memory_mapped": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Slave_interface_Clock_enable_memory_mapped": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Output_Register_Type": [{"value": "Embedded_Reg", "resolve_type": "user", "enabled": false, "usage": "all"}], "Enable_Safety_Circuit": [{"value": "false", "resolve_type": "user", "format": "bool", "enabled": false, "usage": "all"}], "Enable_ECC_Type": [{"value": "Hard_ECC", "resolve_type": "user", "enabled": false, "usage": "all"}], "C_SELECT_XPM": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "all"}]}, "model_parameters": {"C_COMMON_CLOCK": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SELECT_XPM": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_COUNT_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DATA_COUNT_WIDTH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DEFAULT_VALUE": [{"value": "BlankString", "resolve_type": "generated", "usage": "all"}], "C_DIN_WIDTH": [{"value": "64", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DOUT_RST_VAL": [{"value": "0", "resolve_type": "generated", "usage": "all"}], "C_DOUT_WIDTH": [{"value": "64", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ENABLE_RLOCS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_FAMILY": [{"value": "artix7", "resolve_type": "generated", "usage": "all"}], "C_FULL_FLAGS_RST_VAL": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_ALMOST_EMPTY": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_ALMOST_FULL": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_BACKUP": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_DATA_COUNT": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_INT_CLK": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_MEMINIT_FILE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_OVERFLOW": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_RD_DATA_COUNT": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_RD_RST": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_RST": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_SRST": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_UNDERFLOW": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_VALID": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_WR_ACK": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_WR_DATA_COUNT": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_WR_RST": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_IMPLEMENTATION_TYPE": [{"value": "2", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_INIT_WR_PNTR_VAL": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MEMORY_TYPE": [{"value": "2", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MIF_FILE_NAME": [{"value": "BlankString", "resolve_type": "generated", "usage": "all"}], "C_OPTIMIZATION_MODE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_OVERFLOW_LOW": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PRELOAD_LATENCY": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PRELOAD_REGS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PRIM_FIFO_TYPE": [{"value": "512x72", "resolve_type": "generated", "usage": "all"}], "C_PROG_EMPTY_THRESH_ASSERT_VAL": [{"value": "2", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_THRESH_NEGATE_VAL": [{"value": "3", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_THRESH_ASSERT_VAL": [{"value": "13", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_THRESH_NEGATE_VAL": [{"value": "12", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_RD_DATA_COUNT_WIDTH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_RD_DEPTH": [{"value": "16", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_RD_FREQ": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_RD_PNTR_WIDTH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_UNDERFLOW_LOW": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_DOUT_RST": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_ECC": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_EMBEDDED_REG": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_PIPELINE_REG": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_POWER_SAVING_MODE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_FIFO16_FLAGS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_FWFT_DATA_COUNT": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_VALID_LOW": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_ACK_LOW": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_DATA_COUNT_WIDTH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_DEPTH": [{"value": "16", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_FREQ": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_PNTR_WIDTH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_RESPONSE_LATENCY": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_MSGON_VAL": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ENABLE_RST_SYNC": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_EN_SAFETY_CKT": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ERROR_INJECTION_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_SYNCHRONIZER_STAGE": [{"value": "2", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_INTERFACE_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_TYPE": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXI_WR_CHANNEL": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXI_RD_CHANNEL": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_SLAVE_CE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_MASTER_CE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ADD_NGC_CONSTRAINT": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_COMMON_OVERFLOW": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_COMMON_UNDERFLOW": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_DEFAULT_SETTINGS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_ID_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_ADDR_WIDTH": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_DATA_WIDTH": [{"value": "64", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_LEN_WIDTH": [{"value": "8", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_LOCK_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXI_ID": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXI_AWUSER": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXI_WUSER": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXI_BUSER": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXI_ARUSER": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXI_RUSER": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_ARUSER_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_AWUSER_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_WUSER_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_BUSER_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXI_RUSER_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXIS_TDATA": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXIS_TID": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXIS_TDEST": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXIS_TUSER": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXIS_TREADY": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXIS_TLAST": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXIS_TSTRB": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_AXIS_TKEEP": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXIS_TDATA_WIDTH": [{"value": "8", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXIS_TID_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXIS_TDEST_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXIS_TUSER_WIDTH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXIS_TSTRB_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXIS_TKEEP_WIDTH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WACH_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WDCH_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WRCH_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_RACH_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_RDCH_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_AXIS_TYPE": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_IMPLEMENTATION_TYPE_WACH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_IMPLEMENTATION_TYPE_WDCH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_IMPLEMENTATION_TYPE_WRCH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_IMPLEMENTATION_TYPE_RACH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_IMPLEMENTATION_TYPE_RDCH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_IMPLEMENTATION_TYPE_AXIS": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_APPLICATION_TYPE_WACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_APPLICATION_TYPE_WDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_APPLICATION_TYPE_WRCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_APPLICATION_TYPE_RACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_APPLICATION_TYPE_RDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_APPLICATION_TYPE_AXIS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PRIM_FIFO_TYPE_WACH": [{"value": "512x36", "resolve_type": "generated", "usage": "all"}], "C_PRIM_FIFO_TYPE_WDCH": [{"value": "1kx36", "resolve_type": "generated", "usage": "all"}], "C_PRIM_FIFO_TYPE_WRCH": [{"value": "512x36", "resolve_type": "generated", "usage": "all"}], "C_PRIM_FIFO_TYPE_RACH": [{"value": "512x36", "resolve_type": "generated", "usage": "all"}], "C_PRIM_FIFO_TYPE_RDCH": [{"value": "1kx36", "resolve_type": "generated", "usage": "all"}], "C_PRIM_FIFO_TYPE_AXIS": [{"value": "1kx18", "resolve_type": "generated", "usage": "all"}], "C_USE_ECC_WACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_ECC_WDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_ECC_WRCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_ECC_RACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_ECC_RDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_USE_ECC_AXIS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ERROR_INJECTION_TYPE_WACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ERROR_INJECTION_TYPE_WDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ERROR_INJECTION_TYPE_WRCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ERROR_INJECTION_TYPE_RACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ERROR_INJECTION_TYPE_RDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_ERROR_INJECTION_TYPE_AXIS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DIN_WIDTH_WACH": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DIN_WIDTH_WDCH": [{"value": "64", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DIN_WIDTH_WRCH": [{"value": "2", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DIN_WIDTH_RACH": [{"value": "32", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DIN_WIDTH_RDCH": [{"value": "64", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_DIN_WIDTH_AXIS": [{"value": "1", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_DEPTH_WACH": [{"value": "16", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_DEPTH_WDCH": [{"value": "1024", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_DEPTH_WRCH": [{"value": "16", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_DEPTH_RACH": [{"value": "16", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_DEPTH_RDCH": [{"value": "1024", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_DEPTH_AXIS": [{"value": "1024", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_PNTR_WIDTH_WACH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_PNTR_WIDTH_WDCH": [{"value": "10", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_PNTR_WIDTH_WRCH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_PNTR_WIDTH_RACH": [{"value": "4", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_PNTR_WIDTH_RDCH": [{"value": "10", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_WR_PNTR_WIDTH_AXIS": [{"value": "10", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_DATA_COUNTS_WACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_DATA_COUNTS_WDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_DATA_COUNTS_WRCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_DATA_COUNTS_RACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_DATA_COUNTS_RDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_DATA_COUNTS_AXIS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_PROG_FLAGS_WACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_PROG_FLAGS_WDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_PROG_FLAGS_WRCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_PROG_FLAGS_RACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_PROG_FLAGS_RDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_HAS_PROG_FLAGS_AXIS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_TYPE_WACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_TYPE_WDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_TYPE_WRCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_TYPE_RACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_TYPE_RDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_TYPE_AXIS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_THRESH_ASSERT_VAL_WACH": [{"value": "1023", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_THRESH_ASSERT_VAL_WDCH": [{"value": "1023", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_THRESH_ASSERT_VAL_WRCH": [{"value": "1023", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_THRESH_ASSERT_VAL_RACH": [{"value": "1023", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_THRESH_ASSERT_VAL_RDCH": [{"value": "1023", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_FULL_THRESH_ASSERT_VAL_AXIS": [{"value": "1023", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_TYPE_WACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_TYPE_WDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_TYPE_WRCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_TYPE_RACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_TYPE_RDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_TYPE_AXIS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_THRESH_ASSERT_VAL_WACH": [{"value": "1022", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_THRESH_ASSERT_VAL_WDCH": [{"value": "1022", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_THRESH_ASSERT_VAL_WRCH": [{"value": "1022", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_THRESH_ASSERT_VAL_RACH": [{"value": "1022", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_THRESH_ASSERT_VAL_RDCH": [{"value": "1022", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_PROG_EMPTY_THRESH_ASSERT_VAL_AXIS": [{"value": "1022", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_REG_SLICE_MODE_WACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_REG_SLICE_MODE_WDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_REG_SLICE_MODE_WRCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_REG_SLICE_MODE_RACH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_REG_SLICE_MODE_RDCH": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}], "C_REG_SLICE_MODE_AXIS": [{"value": "0", "resolve_type": "generated", "format": "long", "usage": "all"}]}, "project_parameters": {"ARCHITECTURE": [{"value": "artix7"}], "BASE_BOARD_PART": [{"value": ""}], "BOARD_CONNECTIONS": [{"value": ""}], "DEVICE": [{"value": "xc7a75t"}], "PACKAGE": [{"value": "fgg484"}], "PREFHDL": [{"value": "VERILOG"}], "SILICON_REVISION": [{"value": ""}], "SIMULATOR_LANGUAGE": [{"value": "MIXED"}], "SPEEDGRADE": [{"value": "-2"}], "STATIC_POWER": [{"value": ""}], "TEMPERATURE_GRADE": [{"value": ""}], "USE_RDI_CUSTOMIZATION": [{"value": "TRUE"}], "USE_RDI_GENERATION": [{"value": "TRUE"}]}, "runtime_parameters": {"IPCONTEXT": [{"value": "IP_Flow"}], "IPREVISION": [{"value": "9"}], "MANAGED": [{"value": "TRUE"}], "OUTPUTDIR": [{"value": "../../../../pcileech_enigma_x1.gen/sources_1/ip/fifo_64_64_clk2_comrx"}], "SELECTEDSIMMODEL": [{"value": ""}], "SHAREDDIR": [{"value": "."}], "SWVERSION": [{"value": "2023.2"}], "SYNTHESISFLOW": [{"value": "OUT_OF_CONTEXT"}]}}, "boundary": {"ports": {"rst": [{"direction": "in", "driver_value": "0"}], "wr_clk": [{"direction": "in", "driver_value": "0"}], "rd_clk": [{"direction": "in", "driver_value": "0"}], "din": [{"direction": "in", "size_left": "63", "size_right": "0", "driver_value": "0"}], "wr_en": [{"direction": "in", "driver_value": "0"}], "rd_en": [{"direction": "in", "driver_value": "0"}], "dout": [{"direction": "out", "size_left": "63", "size_right": "0", "driver_value": "0"}], "full": [{"direction": "out", "driver_value": "0x0"}], "empty": [{"direction": "out", "driver_value": "0x1"}], "valid": [{"direction": "out", "driver_value": "0x0"}]}, "interfaces": {"write_clk": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "slave", "parameters": {"FREQ_HZ": [{"value": "100000000", "resolve_type": "user", "format": "long", "usage": "all"}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_BUSIF": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK": [{"physical_name": "wr_clk"}]}}, "read_clk": {"vlnv": "xilinx.com:signal:clock:1.0", "abstraction_type": "xilinx.com:signal:clock_rtl:1.0", "mode": "slave", "parameters": {"FREQ_HZ": [{"value": "100000000", "resolve_type": "user", "format": "long", "usage": "all"}], "FREQ_TOLERANCE_HZ": [{"value": "0", "resolve_type": "generated", "format": "long", "is_ips_inferred": true, "is_static_object": false}], "PHASE": [{"value": "0.0", "resolve_type": "generated", "format": "float", "is_ips_inferred": true, "is_static_object": false}], "CLK_DOMAIN": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_BUSIF": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_PORT": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "ASSOCIATED_RESET": [{"value": "", "resolve_type": "generated", "is_ips_inferred": true, "is_static_object": false}], "INSERT_VIP": [{"value": "0", "resolve_type": "user", "format": "long", "usage": "simulation.rtl", "is_ips_inferred": true, "is_static_object": false}]}, "port_maps": {"CLK": [{"physical_name": "rd_clk"}]}}, "FIFO_WRITE": {"vlnv": "xilinx.com:interface:fifo_write:1.0", "abstraction_type": "xilinx.com:interface:fifo_write_rtl:1.0", "mode": "slave", "port_maps": {"FULL": [{"physical_name": "full"}], "WR_DATA": [{"physical_name": "din"}], "WR_EN": [{"physical_name": "wr_en"}]}}, "FIFO_READ": {"vlnv": "xilinx.com:interface:fifo_read:1.0", "abstraction_type": "xilinx.com:interface:fifo_read_rtl:1.0", "mode": "slave", "port_maps": {"EMPTY": [{"physical_name": "empty"}], "RD_DATA": [{"physical_name": "dout"}], "RD_EN": [{"physical_name": "rd_en"}]}}}}}}